"""
Test file for user management edge cases:
1. Admin role demotion - user should be set to pending_assignment and removed from teams
2. User removed from team - status should be set to pending_assignment if no teams left
3. Team deletion - users should have status updated if they're not in any other teams
4. Authentication validation - non-admin users must be assigned to teams to login
"""
import pytest
import requests
from requests.exceptions import HTTPError

from onyx.auth.schemas import User<PERSON>ole
from tests.integration.common_utils.managers.user import UserManager
from tests.integration.common_utils.managers.user_group import UserGroupManager
from tests.integration.common_utils.test_models import DATestUser
from tests.integration.common_utils.constants import API_SERVER_URL


def test_admin_role_demotion_edge_case(reset: None) -> None:
    """Test that when an admin is demoted to non-admin role, they are set to pending_assignment status
    and removed from all teams, preventing login until reassigned to a team."""
    
    # Create admin user (first user is automatically admin)
    admin_user: DATestUser = UserManager.create(name="admin_user")
    assert UserManager.is_role(admin_user, UserRole.ADMIN)
    
    # Create another admin to perform the demotion
    admin_user_2: DATestUser = UserManager.create(name="admin_user_2")
    UserManager.set_role(admin_user_2, UserRole.ADMIN, admin_user)
    
    # Create a team and add the first admin to it (this shouldn't normally happen, but for testing)
    user_group = UserGroupManager.create(
        name="test_group",
        user_ids=[admin_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user_2,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group], user_performing_action=admin_user_2
    )
    
    # Demote admin to basic user
    demoted_user = UserManager.set_role(admin_user, UserRole.BASIC, admin_user_2)
    assert UserManager.is_role(demoted_user, UserRole.BASIC)
    
    # Try to login as demoted user - should fail because they need team assignment
    with pytest.raises(HTTPError) as exc_info:
        UserManager.login_as_user(demoted_user)
    
    # The error should indicate they need team assignment
    assert exc_info.value.response.status_code in [401, 403]


def test_user_removed_from_all_teams_edge_case(reset: None) -> None:
    """Test that when a non-admin user is removed from all teams, 
    their status is set to pending_assignment and they cannot login."""
    
    # Create admin user
    admin_user: DATestUser = UserManager.create(name="admin_user")
    
    # Create basic user
    basic_user: DATestUser = UserManager.create(name="basic_user")
    assert UserManager.is_role(basic_user, UserRole.BASIC)
    
    # Create a team and add the basic user
    user_group = UserGroupManager.create(
        name="test_group",
        user_ids=[basic_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group], user_performing_action=admin_user
    )
    
    # User should be able to login when in a team
    logged_in_user = UserManager.login_as_user(basic_user)
    assert logged_in_user is not None
    
    # Remove user from the team
    UserGroupManager.edit(
        user_group_to_edit=user_group,
        user_ids=[],  # Empty list removes all users
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group], user_performing_action=admin_user
    )
    
    # Try to login as user - should fail because they're not in any team
    with pytest.raises(HTTPError) as exc_info:
        UserManager.login_as_user(basic_user)
    
    # The error should indicate they need team assignment
    assert exc_info.value.response.status_code in [401, 403]


def test_team_deletion_edge_case(reset: None) -> None:
    """Test that when a team is deleted, non-admin users who are not in any other teams
    have their status updated to pending_assignment and cannot login."""
    
    # Create admin user
    admin_user: DATestUser = UserManager.create(name="admin_user")
    
    # Create basic user
    basic_user: DATestUser = UserManager.create(name="basic_user")
    assert UserManager.is_role(basic_user, UserRole.BASIC)
    
    # Create a team and add the basic user
    user_group = UserGroupManager.create(
        name="test_group_to_delete",
        user_ids=[basic_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group], user_performing_action=admin_user
    )
    
    # User should be able to login when in a team
    logged_in_user = UserManager.login_as_user(basic_user)
    assert logged_in_user is not None
    
    # Delete the team
    UserGroupManager.delete(
        user_group_to_delete=user_group,
        user_performing_action=admin_user,
    )
    
    # Try to login as user - should fail because their team was deleted
    with pytest.raises(HTTPError) as exc_info:
        UserManager.login_as_user(basic_user)
    
    # The error should indicate they need team assignment
    assert exc_info.value.response.status_code in [401, 403]


def test_user_in_multiple_teams_removal(reset: None) -> None:
    """Test that when a user is in multiple teams and one team is deleted or they're removed from one,
    they can still login if they're in at least one other team."""
    
    # Create admin user
    admin_user: DATestUser = UserManager.create(name="admin_user")
    
    # Create basic user
    basic_user: DATestUser = UserManager.create(name="basic_user")
    assert UserManager.is_role(basic_user, UserRole.BASIC)
    
    # Create two teams and add the basic user to both
    user_group_1 = UserGroupManager.create(
        name="test_group_1",
        user_ids=[basic_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    
    user_group_2 = UserGroupManager.create(
        name="test_group_2", 
        user_ids=[basic_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group_1, user_group_2], 
        user_performing_action=admin_user
    )
    
    # User should be able to login when in teams
    logged_in_user = UserManager.login_as_user(basic_user)
    assert logged_in_user is not None
    
    # Delete one team
    UserGroupManager.delete(
        user_group_to_delete=user_group_1,
        user_performing_action=admin_user,
    )
    
    # User should still be able to login because they're in the second team
    logged_in_user = UserManager.login_as_user(basic_user)
    assert logged_in_user is not None
    
    # Remove user from the second team
    UserGroupManager.edit(
        user_group_to_edit=user_group_2,
        user_ids=[],  # Empty list removes all users
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group_2], user_performing_action=admin_user
    )
    
    # Now user should not be able to login
    with pytest.raises(HTTPError) as exc_info:
        UserManager.login_as_user(basic_user)
    
    # The error should indicate they need team assignment
    assert exc_info.value.response.status_code in [401, 403]


def test_admin_users_not_affected_by_team_requirements(reset: None) -> None:
    """Test that admin users can login regardless of team assignments."""

    # Create admin user
    admin_user: DATestUser = UserManager.create(name="admin_user")
    assert UserManager.is_role(admin_user, UserRole.ADMIN)

    # Admin should be able to login without being in any team
    logged_in_user = UserManager.login_as_user(admin_user)
    assert logged_in_user is not None

    # Create a team and add admin (this shouldn't normally happen)
    user_group = UserGroupManager.create(
        name="admin_test_group",
        user_ids=[],  # Don't add admin to avoid validation error
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )

    # Admin should still be able to login
    logged_in_user = UserManager.login_as_user(admin_user)
    assert logged_in_user is not None


def test_admin_demotion_and_promotion_cycle(reset: None) -> None:
    """Test the complete cycle: admin -> demote -> promote back to admin."""

    # Create admin user (first user is automatically admin)
    admin_user: DATestUser = UserManager.create(name="admin_user")
    assert UserManager.is_role(admin_user, UserRole.ADMIN)

    # Create another admin to perform the role changes
    admin_user_2: DATestUser = UserManager.create(name="admin_user_2")
    UserManager.set_role(admin_user_2, UserRole.ADMIN, admin_user)

    # Step 1: Demote admin to basic user
    demoted_user = UserManager.set_role(admin_user, UserRole.BASIC, admin_user_2)
    assert UserManager.is_role(demoted_user, UserRole.BASIC)

    # User should not be able to login (pending_assignment status)
    with pytest.raises(HTTPError) as exc_info:
        UserManager.login_as_user(demoted_user)
    assert exc_info.value.response.status_code in [401, 403]

    # Step 2: Promote back to admin
    promoted_user = UserManager.set_role(demoted_user, UserRole.ADMIN, admin_user_2)
    assert UserManager.is_role(promoted_user, UserRole.ADMIN)

    # User should now be able to login again (status should be active)
    logged_in_user = UserManager.login_as_user(promoted_user)
    assert logged_in_user is not None


def test_basic_to_admin_promotion(reset: None) -> None:
    """Test promoting a basic user (who was in teams) to admin."""

    # Create admin user
    admin_user: DATestUser = UserManager.create(name="admin_user")

    # Create basic user
    basic_user: DATestUser = UserManager.create(name="basic_user")
    assert UserManager.is_role(basic_user, UserRole.BASIC)

    # Create a team and add the basic user
    user_group = UserGroupManager.create(
        name="test_group",
        user_ids=[basic_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )
    UserGroupManager.wait_for_sync(
        user_groups_to_check=[user_group], user_performing_action=admin_user
    )

    # User should be able to login when in a team
    logged_in_user = UserManager.login_as_user(basic_user)
    assert logged_in_user is not None

    # Promote basic user to admin
    promoted_user = UserManager.set_role(basic_user, UserRole.ADMIN, admin_user)
    assert UserManager.is_role(promoted_user, UserRole.ADMIN)

    # User should still be able to login as admin (removed from teams but status active)
    logged_in_user = UserManager.login_as_user(promoted_user)
    assert logged_in_user is not None
