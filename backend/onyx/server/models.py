from typing import Generic
from typing import <PERSON><PERSON>
from typing import TypeVar
from uuid import <PERSON>UID

from pydantic import BaseModel

from onyx.auth.schemas import UserRole
from onyx.db.models import User


DataT = TypeVar("DataT")


class StatusResponse(BaseModel, Generic[DataT]):
    success: bool
    message: Optional[str] = None
    data: Optional[DataT] = None


class ApiKey(BaseModel):
    api_key: str


class IdReturn(BaseModel):
    id: int


class MinimalUserSnapshot(BaseModel):
    id: UUID
    email: str

class UserIdSnapshot(BaseModel):
    id: UUID

class FullUserSnapshot(BaseModel):
    id: UUID
    email: str
    role: UserRole
    is_active: bool
    password_configured: bool
    effective_status: str  # "active", "pending_assignment", "inactive", "ready_to_signup"

    @classmethod
    def from_user_model(cls, user: User, db_session=None) -> "FullUserSnapshot":
        # Determine effective status based on user.is_active and InvitedUser.status
        effective_status = "inactive"  # Default

        # IMPORTANT: <PERSON><PERSON> should NEVER show as "pending_assignment" in the UI
        # They should always show as "active" if their account is active
        if user.role == UserRole.ADMIN or user.role == "admin":
            effective_status = "active" if user.is_active else "inactive"
        elif user.is_active:
            # For non-admin users, check InvitedUser status
            if db_session:
                from onyx.db.models import InvitedUser
                invited_user = db_session.query(InvitedUser).filter_by(id=user.id).first()
                if invited_user:
                    if invited_user.status == "active":
                        effective_status = "active"
                    elif invited_user.status == "pending_assignment":
                        effective_status = "pending_assignment"
                    elif invited_user.status == "ready_to_signup":
                        effective_status = "ready_to_signup"
                    else:
                        effective_status = "inactive"
                else:
                    # No InvitedUser record, assume inactive
                    effective_status = "inactive"
            else:
                # If no db_session provided, fall back to basic logic
                effective_status = "active" if user.is_active else "inactive"
        else:
            effective_status = "inactive"

        return cls(
            id=user.id,
            email=user.email,
            role=user.role,
            is_active=user.is_active,
            password_configured=user.password_configured,
            effective_status=effective_status,
        )


class InvitedUserSnapshot(BaseModel):
    email: str
    role: UserRole | None = None
    status: str | None = None
    user_id: str | None = None


class DisplayPriorityRequest(BaseModel):
    display_priority_map: dict[int, int]
